import { ThemeProvider } from '@/context/theme-context';
import { useDarkMode } from '@/hooks/use-dark-mode';
import React from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

function AchieveContent() {
  const { isDark } = useDarkMode();
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { backgroundColor: isDark ? '#000000' : '#f8f7f6', paddingTop: insets.top, paddingBottom: insets.bottom }]}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={[styles.title, { color: isDark ? '#ffffff' : '#000000' }]}>Achieve</Text>
          <Text style={[styles.subtitle, { color: isDark ? '#cccccc' : '#666666' }]}>
            Goals and achievements
          </Text>
        </View>

        <View style={styles.content}>
          <Text style={[styles.description, { color: isDark ? '#cccccc' : '#666666' }]}>
            This is where your goals and achievements will be displayed.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

export default function AchieveScreen() {
  return (
    <ThemeProvider>
        <AchieveContent />
    </ThemeProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    paddingBottom: 120, // Space for floating navbar
  },
  header: {
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
  },
  content: {
    flex: 1,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
  },
});
