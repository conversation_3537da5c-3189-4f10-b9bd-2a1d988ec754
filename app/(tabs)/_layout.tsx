import { FloatingTabBar } from '@/components/ui/floating-tab-bar';
import { Ionicons } from '@expo/vector-icons';
import { Tabs, useRouter, useSegments } from 'expo-router';
import React from 'react';

export default function TabLayout() {
  const router = useRouter();
  const segments = useSegments();
  const currentRoute = segments[segments.length - 1];

  const tabs = [
    {
      name: 'index',
      icon: 'home-outline' as keyof typeof Ionicons.glyphMap,
      iconFocused: 'home' as keyof typeof Ionicons.glyphMap,
      onPress: () => router.push('/(tabs)' as any),
      isFocused: currentRoute === '(tabs)' || !currentRoute,
    },
    {
      name: 'focus',
      icon: 'timer-outline' as keyof typeof Ionicons.glyphMap,
      iconFocused: 'timer' as keyof typeof Ionicons.glyphMap,
      onPress: () => router.push('/(tabs)/focus' as any),
      isFocused: currentRoute === 'focus',
    },
    {
      name: 'track',
      icon: 'bar-chart-outline' as keyof typeof Ionicons.glyphMap,
      iconFocused: 'bar-chart' as keyof typeof Ionicons.glyphMap,
      onPress: () => router.push('/(tabs)/track' as any),
      isFocused: currentRoute === 'track',
    },
    {
      name: 'achieve',
      icon: 'trophy-outline' as keyof typeof Ionicons.glyphMap,
      iconFocused: 'trophy' as keyof typeof Ionicons.glyphMap,
      onPress: () => router.push('/(tabs)/achieve' as any),
      isFocused: currentRoute === 'achieve',
    },
    {
      name: 'tasks',
      icon: 'list-outline' as keyof typeof Ionicons.glyphMap,
      iconFocused: 'list' as keyof typeof Ionicons.glyphMap,
      onPress: () => router.push('/(tabs)/tasks' as any),
      isFocused: currentRoute === 'tasks',
    },
  ];

  return (
    <>
      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarStyle: { display: 'none' }, // Hide the default tab bar
        }}
      >
        <Tabs.Screen name="index" />
        <Tabs.Screen name="focus" />
        <Tabs.Screen name="track" />
        <Tabs.Screen name="achieve" />
        <Tabs.Screen name="tasks" />
      </Tabs>
      <FloatingTabBar tabs={tabs} />
    </>
  );
}
