<!DOCTYPE html>

<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>IsotopeAI Tasks</title>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>

<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<script>tailwind.config = {darkMode: "class", theme: {extend: {colors: {primary: "#F4C753", "background-light": "#f8f7f6", "background-dark": "#221d10"}, fontFamily: {display: "Inter"}, borderRadius: {DEFAULT: "0.5rem", lg: "1rem", xl: "1.5rem", full: "9999px"}}}};</script>

<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
<style>
    body {
      min-height: max(884px, 100dvh);
    }
  </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display">
<div class="flex flex-col h-screen justify-between">
<main class="flex-grow overflow-y-auto">
<header class="sticky top-0 z-10 bg-gradient-to-b from-background-light/95 to-background-light/80 dark:from-background-dark/95 dark:to-background-dark/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
<div class="flex items-center justify-between px-6 py-4">
<div class="w-10"></div>
<div class="flex flex-col items-center">
<h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">Tasks</h1>
<p class="text-xs text-gray-500 dark:text-gray-400 font-medium">& Habits</p>
</div>
<button class="flex h-10 w-10 items-center justify-center rounded-xl bg-primary/10 text-primary hover:bg-primary/20 transition-colors">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
<path stroke-linecap="round" stroke-linejoin="round" d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
</svg>
</button>
</div>
<div class="px-4 pb-2">
<div class="flex items-center gap-2">
<button class="flex items-center gap-1.5 px-3 py-1.5 bg-slate-200 dark:bg-slate-800 rounded-full text-slate-700 dark:text-slate-200 text-sm font-medium">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
<path stroke-linecap="round" stroke-linejoin="round" d="M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"/>
</svg>
                            Sort
                        </button>
<button class="flex items-center gap-1.5 px-3 py-1.5 bg-slate-200 dark:bg-slate-800 rounded-full text-slate-700 dark:text-slate-200 text-sm font-medium">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
<path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"/>
</svg>
                            Filter
                        </button>
</div>
</div>
</header>
<div class="px-4">
<div class="bg-white dark:bg-slate-800/50 p-4 rounded-xl mt-4">
<h3 class="text-base font-bold text-slate-900 dark:text-white mb-2">Smart Suggestions</h3>
<div class="flex gap-2">
<button class="flex-1 text-left bg-slate-100 dark:bg-slate-700/50 p-3 rounded-lg text-sm">
<p class="font-semibold text-slate-800 dark:text-white">Plan 'Project Y'</p>
<p class="text-xs text-slate-500 dark:text-slate-400">Based on your goals</p>
</button>
<button class="flex-1 text-left bg-slate-100 dark:bg-slate-700/50 p-3 rounded-lg text-sm">
<p class="font-semibold text-slate-800 dark:text-white">Evening Walk</p>
<p class="text-xs text-slate-500 dark:text-slate-400">Trending habit</p>
</button>
</div>
</div>
<div class="flex items-center justify-between mt-6 mb-3">
<h2 class="text-xl font-bold text-slate-900 dark:text-white">Today's List</h2>
<a class="text-sm font-medium text-primary dark:text-primary" href="#">View All</a>
</div>
<ul class="space-y-2">
<li class="flex items-center justify-between bg-white dark:bg-slate-800/50 p-3 rounded-lg">
<div class="flex items-center gap-4">
<button class="relative flex items-center justify-center size-10 shrink-0">
<div class="absolute inset-0 rounded-full border-2 border-primary/20 dark:border-primary/40"></div>
<div class="absolute inset-1 rounded-full border-2 border-primary/20 dark:border-primary/40 transform rotate-45"></div>
</button>
<div>
<p class="font-semibold text-slate-800 dark:text-white">Morning Meditation</p>
<p class="text-sm text-slate-500 dark:text-slate-400">8:00 AM</p>
</div>
</div>
<div class="flex items-center gap-2">
<span class="text-xs font-semibold text-primary/80 dark:text-primary/70">🔥 5</span>
<button class="w-6 h-6 rounded-full border-2 border-slate-300 dark:border-slate-600 flex items-center justify-center"></button>
</div>
</li>
<li class="flex items-center justify-between bg-white dark:bg-slate-800/50 p-3 rounded-lg opacity-50">
<div class="flex items-center gap-4">
<button class="relative flex items-center justify-center size-10 shrink-0">
<div class="absolute inset-0 rounded-full bg-primary/20 dark:bg-primary/30"></div>
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-primary">
<path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5"/>
</svg>
</button>
<div>
<p class="font-semibold text-slate-800 dark:text-white line-through">Review Daily Goals</p>
<p class="text-sm text-slate-500 dark:text-slate-400">9:00 AM</p>
</div>
</div>
</li>
<li class="flex items-center justify-between bg-white dark:bg-slate-800/50 p-3 rounded-lg">
<div class="flex items-center gap-4">
<button class="relative flex items-center justify-center size-10 shrink-0">
<div class="w-full h-full rounded-full border-3 border-primary/20 dark:border-primary/40 relative">
<div class="absolute inset-0 rounded-full border-3 border-primary border-r-transparent border-b-transparent transform rotate-45"></div>
<div class="absolute inset-2 bg-white dark:bg-slate-800 rounded-full flex items-center justify-center">
<span class="text-xs font-bold text-primary">75%</span>
</div>
</div>
</button>
<div>
<p class="font-semibold text-slate-800 dark:text-white">Project X - Phase 1</p>
<div class="flex items-center gap-2 mt-0.5">
<p class="text-sm text-slate-500 dark:text-slate-400">10:00 AM</p>
<div class="flex -space-x-2">
<img alt="" class="inline-block h-5 w-5 rounded-full ring-2 ring-white dark:ring-slate-800/50" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
<img alt="" class="inline-block h-5 w-5 rounded-full ring-2 ring-white dark:ring-slate-800/50" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
</div>
</div>
</div>
</div>
<button class="w-6 h-6 rounded-full border-2 border-slate-300 dark:border-slate-600 flex items-center justify-center"></button>
</li>
<li class="flex items-center justify-between bg-white dark:bg-slate-800/50 p-3 rounded-lg">
<div class="flex items-center gap-4">
<button class="relative flex items-center justify-center size-10 shrink-0">
<div class="absolute inset-0 rounded-full border-2 border-orange-500/30 dark:border-orange-400/40"></div>
</button>
<div>
<p class="font-semibold text-slate-800 dark:text-white">Client Meeting</p>
<p class="text-sm text-slate-500 dark:text-slate-400">11:00 AM</p>
</div>
</div>
<button class="w-6 h-6 rounded-full border-2 border-slate-300 dark:border-slate-600 flex items-center justify-center"></button>
</li>
</ul>
<div class="bg-white dark:bg-slate-800/50 p-4 rounded-xl mt-6 mb-4">
<h3 class="text-base font-bold text-slate-900 dark:text-white mb-3">Leaderboards</h3>
<div class="space-y-3">
<div class="flex items-center gap-3">
<span class="text-lg font-bold text-slate-400 dark:text-slate-500">1</span>
<img alt="" class="h-10 w-10 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
<div class="flex-1">
<p class="font-semibold text-slate-800 dark:text-white">You</p>
<p class="text-sm text-slate-500 dark:text-slate-400">Streak: 24 days</p>
</div>
<div class="text-right">
<p class="font-semibold text-primary">1250 pts</p>
<span class="text-xs text-green-500">+50 today</span>
</div>
</div>
<div class="flex items-center gap-3">
<span class="text-lg font-bold text-slate-400 dark:text-slate-500">2</span>
<img alt="" class="h-10 w-10 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
<div class="flex-1">
<p class="font-semibold text-slate-800 dark:text-white">Jane Doe</p>
<p class="text-sm text-slate-500 dark:text-slate-400">Streak: 21 days</p>
</div>
<p class="font-semibold text-slate-600 dark:text-slate-300">1180 pts</p>
</div>
<div class="flex items-center gap-3">
<span class="text-lg font-bold text-slate-400 dark:text-slate-500">3</span>
<img alt="" class="h-10 w-10 rounded-full" src="https://lh3.googleusercontent.com/aida-public/AB6AXuC5GgWux47ci7kGJ2F1OzXVlfoAx1Qljl1Cp1ygLR8Nm-XWL9o-yuy72tDfMsCps6AeYh13NqTIJbaYPFXC3IfrFltF2jM3r2JxMDZfL73Ps8BB6Jdhb1QQ8yHKrRuxc1KlG1i1w5u_eOQJw8YFYtQz6j0IYM67uExmdHA0A4daq3R8vnRfFwgy8MonALN-rTzsZEgWy8TWHVynAQ08HOAurr4uLLFOPXXUu9W9bk7PdekEuwl00WVNR23lRVi-E9zTsl9PM2vWzp8"/>
<div class="flex-1">
<p class="font-semibold text-slate-800 dark:text-white">John Smith</p>
<p class="text-sm text-slate-500 dark:text-slate-400">Streak: 18 days</p>
</div>
<p class="font-semibold text-slate-600 dark:text-slate-300">1050 pts</p>
</div>
</div>
</div>
</div>
</main>
<footer class="sticky bottom-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg">
<div class="flex justify-around items-center px-4 py-3 safe-area-inset-bottom">
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="dashboard.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
</svg>
</div>
<p class="text-xs font-medium">Home</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="focus.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
</svg>
</div>
<p class="text-xs font-medium">Focus</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="track.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"/>
</svg>
</div>
<p class="text-xs font-medium">Track</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="achieve.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"/>
</svg>
</div>
<p class="text-xs font-medium">Achieve</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl bg-primary/10 text-primary" href="tasks.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"/>
</svg>
</div>
<p class="text-xs font-semibold">Tasks</p>
</a>
</div>
</footer>
</div>
</body></html>