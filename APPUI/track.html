<!DOCTYPE html>

<html class="dark" lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>IsotopeAI: Study Session Analysis</title>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<script>tailwind.config = {darkMode: "class", theme: {extend: {colors: {primary: "#F4C753", "background-light": "#f8f7f6", "background-dark": "#221d10"}, fontFamily: {display: "Inter"}, borderRadius: {DEFAULT: "0.5rem", lg: "1rem", xl: "1.5rem", full: "9999px"}}}};</script>
<style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .chart-bar {
            background: #1173d4;
        }
        .chart-line {
            stroke: #1173d4;
        }
        .chart-area {
            fill: url(#chartGradient);
        }
    </style>
<style>
    /* No min-height for body in track.html */
  </style>
</head>
<body class="bg-background-light dark:bg-background-dark font-display">
<div class="flex h-screen flex-col">
<div>
<header class="sticky top-0 z-10 bg-gradient-to-b from-background-light/95 to-background-light/80 dark:from-background-dark/95 dark:to-background-dark/80 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-800/50">
<div class="flex items-center justify-between px-6 py-4">
<div class="w-10"></div>
<div class="flex flex-col items-center">
<h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">Track</h1>
<p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Analytics</p>
</div>
<button class="flex h-10 w-10 items-center justify-center rounded-xl bg-gray-100/80 dark:bg-gray-800/80 text-gray-600 dark:text-gray-400 hover:bg-gray-200/80 dark:hover:bg-gray-700/80 transition-colors">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="20" height="20">
<path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.*************.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"/>
<path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
</svg>
</button>
</div>
</header>
<main class="flex-1 overflow-y-auto px-6 py-8 space-y-8">
<section class="mb-8">
<h2 class="text-2xl font-bold text-black dark:text-white mb-4">Session Overview</h2>
<div class="grid grid-cols-2 sm:grid-cols-3 gap-4">
<div class="flex flex-col gap-2 rounded-lg bg-primary/10 p-4 text-center">
<p class="text-sm font-medium text-black/70 dark:text-white/70">Total Study Time</p>
<p class="text-2xl font-bold text-black dark:text-white">3h 45m</p>
</div>
<div class="flex flex-col gap-2 rounded-lg bg-primary/10 p-4 text-center">
<p class="text-sm font-medium text-black/70 dark:text-white/70">Focus Score</p>
<p class="text-2xl font-bold text-black dark:text-white">88%</p>
</div>
<div class="flex flex-col gap-2 rounded-lg bg-primary/10 p-4 text-center col-span-2 sm:col-span-1">
<p class="text-sm font-medium text-black/70 dark:text-white/70">Tasks Completed</p>
<p class="text-2xl font-bold text-black dark:text-white">12</p>
</div>
</div>
</section>
<section class="mb-8 rounded-xl bg-background-light p-4 shadow-sm dark:bg-primary/10">
<div class="flex justify-between items-start mb-4">
<div>
<h3 class="text-lg font-bold text-black dark:text-white">Focus Score Trend</h3>
<p class="text-sm text-black/60 dark:text-white/60">Last 7 Days</p>
</div>
<div class="text-right">
<p class="text-3xl font-bold text-black dark:text-white">88%</p>
<p class="text-sm font-medium text-green-500">+5%</p>
</div>
</div>
<div class="h-48 flex items-end justify-around gap-2 px-4">
<div class="bg-gradient-to-t from-primary/60 to-primary/20 rounded-t-lg" style="height: 75%; width: 12px;"></div>
<div class="bg-gradient-to-t from-primary/60 to-primary/20 rounded-t-lg" style="height: 45%; width: 12px;"></div>
<div class="bg-gradient-to-t from-primary/60 to-primary/20 rounded-t-lg" style="height: 85%; width: 12px;"></div>
<div class="bg-gradient-to-t from-primary/60 to-primary/20 rounded-t-lg" style="height: 30%; width: 12px;"></div>
<div class="bg-gradient-to-t from-primary/60 to-primary/20 rounded-t-lg" style="height: 95%; width: 12px;"></div>
<div class="bg-gradient-to-t from-primary/60 to-primary/20 rounded-t-lg" style="height: 60%; width: 12px;"></div>
<div class="bg-gradient-to-t from-primary/60 to-primary/20 rounded-t-lg" style="height: 40%; width: 12px;"></div>
</div>
<div class="flex justify-around text-xs font-bold text-black/60 dark:text-white/60 mt-2">
<p>Mon</p><p>Tue</p><p>Wed</p><p>Thu</p><p>Fri</p><p>Sat</p><p>Sun</p>
</div>
</section>
<section class="mb-8 rounded-xl bg-background-light p-4 shadow-sm dark:bg-primary/10">
<div class="flex justify-between items-start mb-4">
<div>
<h3 class="text-lg font-bold text-black dark:text-white">Task Completion</h3>
<p class="text-sm text-black/60 dark:text-white/60">This Week</p>
</div>
<div class="text-right">
<p class="text-3xl font-bold text-black dark:text-white">12</p>
<p class="text-sm font-medium text-green-500">+10%</p>
</div>
</div>
<div class="grid grid-cols-7 gap-3 items-end h-32 pt-4">
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 70%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 20%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 30%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 40%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 80%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 50%;"></div></div>
<div class="h-full flex items-end"><div class="chart-bar w-full rounded-t" style="height: 100%;"></div></div>
</div>
<div class="flex justify-around text-xs font-bold text-black/60 dark:text-white/60 mt-2">
<p>Mon</p><p>Tue</p><p>Wed</p><p>Thu</p><p>Fri</p><p>Sat</p><p>Sun</p>
</div>
</section>
<section>
<h2 class="text-2xl font-bold text-black dark:text-white mb-4">Task Manager</h2>
<div class="space-y-2">
<div class="flex items-center gap-4 rounded-lg bg-background-light dark:bg-primary/10 p-3">
<input class="h-5 w-5 rounded border-2 border-primary/50 bg-transparent text-primary focus:ring-0 focus:ring-offset-0 checked:bg-primary" type="checkbox"/>
<div>
<p class="font-medium text-black dark:text-white">Complete Calculus Assignment</p>
<p class="text-sm text-black/60 dark:text-white/60">Math</p>
</div>
</div>
<div class="flex items-center gap-4 rounded-lg bg-background-light dark:bg-primary/10 p-3">
<input class="h-5 w-5 rounded border-2 border-primary/50 bg-transparent text-primary focus:ring-0 focus:ring-offset-0 checked:bg-primary" type="checkbox"/>
<div>
<p class="font-medium text-black dark:text-white">Review Physics Notes</p>
<p class="text-sm text-black/60 dark:text-white/60">Science</p>
</div>
</div>
<div class="flex items-center gap-4 rounded-lg bg-background-light dark:bg-primary/10 p-3">
<input checked="" class="h-5 w-5 rounded border-2 border-primary/50 bg-transparent text-primary focus:ring-0 focus:ring-offset-0 checked:bg-primary" type="checkbox"/>
<div>
<p class="font-medium text-black/50 dark:text-white/50 line-through">Read Chapter 5</p>
<p class="text-sm text-black/40 dark:text-white/40 line-through">History</p>
</div>
</div>
<div class="flex items-center gap-4 rounded-lg bg-background-light dark:bg-primary/10 p-3">
<input class="h-5 w-5 rounded border-2 border-primary/50 bg-transparent text-primary focus:ring-0 focus:ring-offset-0 checked:bg-primary" type="checkbox"/>
<div>
<p class="font-medium text-black dark:text-white">Write Essay Outline</p>
<p class="text-sm text-black/60 dark:text-white/60">English</p>
</div>
</div>
</div>
</section>
</main>
</div>
<footer class="sticky bottom-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-800/50 shadow-lg">
<div class="flex justify-around items-center px-4 py-3 safe-area-inset-bottom">
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="dashboard.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
</svg>
</div>
<p class="text-xs font-medium">Home</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="focus.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
</svg>
</div>
<p class="text-xs font-medium">Focus</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl bg-primary/10 text-primary" href="track.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"/>
</svg>
</div>
<p class="text-xs font-semibold">Track</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="achieve.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236V2.721C7.456 2.41 9.71 2.25 12 2.25c2.291 0 4.545.16 6.75.47v1.516M7.73 9.728a6.726 6.726 0 0 0 2.748 1.35m8.272-6.842V4.5c0 2.108-.966 3.99-2.48 5.228m2.48-5.492a46.32 46.32 0 0 1 2.916.52 6.003 6.003 0 0 1-5.395 4.972m0 0a6.726 6.726 0 0 1-2.749 1.35m0 0a6.772 6.772 0 0 1-3.044 0"/>
</svg>
</div>
<p class="text-xs font-medium">Achieve</p>
</a>
<a class="flex flex-col items-center gap-1.5 p-2 rounded-xl text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-800/50 transition-all" href="tasks.html">
<div class="p-1">
<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" width="22" height="22">
<path stroke-linecap="round" stroke-linejoin="round" d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"/>
</svg>
</div>
<p class="text-xs font-medium">Tasks</p>
</a>
</div>
</footer>
</div>
</body></html>