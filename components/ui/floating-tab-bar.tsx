import { Colors } from '@/constants/theme';
import { hexToRgb } from '@/lib/utils';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import React from 'react';
import { Platform, Pressable, View, useColorScheme } from 'react-native';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface TabItem {
  name: string;
  icon: keyof typeof Ionicons.glyphMap;
  iconFocused: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  isFocused: boolean;
}

interface FloatingTabBarProps {
  tabs: TabItem[];
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export function FloatingTabBar({ tabs }: FloatingTabBarProps) {
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme();
  const theme = colorScheme ?? 'light';

  const TabButton = ({ tab, index }: { tab: TabItem; index: number }) => {
    const scale = useSharedValue(1);
    const opacity = useSharedValue(tab.isFocused ? 1 : 0.6);

    const animatedStyle = useAnimatedStyle(() => {
      return {
        transform: [{ scale: scale.value }],
        opacity: opacity.value,
      };
    });

    const backgroundAnimatedStyle = useAnimatedStyle(() => {
      const backgroundColor = interpolate(
        tab.isFocused ? 1 : 0,
        [0, 1],
        [0, 1]
      );
      const selectedTabRgb = hexToRgb(Colors[theme].tabIconSelected);
      
      return {
        backgroundColor: `rgba(${selectedTabRgb}, ${backgroundColor * 0.15})`,
        transform: [{ scale: tab.isFocused ? 1 : 0.8 }],
      };
    });

    const handlePressIn = () => {
      scale.value = withSpring(0.95, {
        damping: 15,
        stiffness: 300,
      });
    };

    const handlePressOut = () => {
      scale.value = withSpring(1, {
        damping: 15,
        stiffness: 300,
      });
    };

    const handlePress = () => {
      tab.onPress();
      // Haptic feedback would go here if needed
    };

    React.useEffect(() => {
      opacity.value = withTiming(tab.isFocused ? 1 : 0.6, {
        duration: 200,
      });
    }, [tab.isFocused]);

    return (
      <AnimatedPressable
        style={[animatedStyle, { flex: 1, alignItems: 'center', justifyContent: 'center' }]}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        onPress={handlePress}
        accessibilityRole="tab"
        accessibilityState={{ selected: tab.isFocused }}
      >
        <Animated.View
          style={[
            backgroundAnimatedStyle,
            {
              position: 'absolute',
              width: 48,
              height: 48,
              borderRadius: 24,
              justifyContent: 'center',
              alignItems: 'center',
            },
          ]}
        />
        <Ionicons
          name={tab.isFocused ? tab.iconFocused : tab.icon}
          size={24}
          color={tab.isFocused ? Colors[theme].tabIconSelected : Colors[theme].tabIconDefault}
        />
      </AnimatedPressable>
    );
  };

  const containerStyle = {
    position: 'absolute' as const,
    bottom: insets.bottom + 16,
    left: 16,
    right: 16,
    height: 64,
    borderRadius: 999, // Make it more rounded
    overflow: 'hidden' as const,
  };

  if (Platform.OS === 'ios') {
    return (
      <View style={containerStyle}>
        <BlurView
          intensity={80}
          tint={theme}
          style={{
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
            paddingHorizontal: 8,
            borderRadius: 999,
            backgroundColor: `rgba(${hexToRgb(Colors[theme].background)}, 0.9)`, // Added semi-transparent background
          }}
        >
          {tabs.map((tab, index) => (
            <TabButton key={tab.name} tab={tab} index={index} />
          ))}
        </BlurView>
      </View>
    );
  }

  // Fallback for Android and other platforms with better dark mode support
  return (
    <View
      style={[
        containerStyle,
        { backgroundColor: `rgba(${hexToRgb(Colors[theme].background)}, 0.9)` }, // Added semi-transparent background
      ]}
    >
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
          paddingHorizontal: 8,
        }}
      >
        {tabs.map((tab, index) => (
          <TabButton key={tab.name} tab={tab} index={index} />
        ))}
      </View>
    </View>
  );
}
