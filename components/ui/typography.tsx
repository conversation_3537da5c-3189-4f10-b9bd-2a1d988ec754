import React from 'react';
import { Text, TextProps, AccessibilityInfo } from 'react-native';
import { cn } from '@/lib/utils';

// Apple's Dynamic Type scale
const typographyScale = {
  largeTitle: {
    fontSize: 34,
    lineHeight: 41,
    fontWeight: '400' as const,
  },
  title1: {
    fontSize: 28,
    lineHeight: 34,
    fontWeight: '400' as const,
  },
  title2: {
    fontSize: 22,
    lineHeight: 28,
    fontWeight: '400' as const,
  },
  title3: {
    fontSize: 20,
    lineHeight: 25,
    fontWeight: '400' as const,
  },
  headline: {
    fontSize: 17,
    lineHeight: 22,
    fontWeight: '600' as const,
  },
  body: {
    fontSize: 17,
    lineHeight: 22,
    fontWeight: '400' as const,
  },
  callout: {
    fontSize: 16,
    lineHeight: 21,
    fontWeight: '400' as const,
  },
  subheadline: {
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '400' as const,
  },
  footnote: {
    fontSize: 13,
    lineHeight: 18,
    fontWeight: '400' as const,
  },
  caption1: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400' as const,
  },
  caption2: {
    fontSize: 11,
    lineHeight: 13,
    fontWeight: '400' as const,
  },
};

type TypographyVariant = keyof typeof typographyScale;

interface TypographyProps extends TextProps {
  variant?: TypographyVariant;
  color?: 'primary' | 'secondary' | 'tertiary' | 'accent' | 'success' | 'warning' | 'error';
  weight?: 'regular' | 'medium' | 'semibold' | 'bold';
  className?: string;
  children: React.ReactNode;
}

export function Typography({
  variant = 'body',
  color = 'primary',
  weight = 'regular',
  className,
  children,
  style,
  ...props
}: TypographyProps) {
  const scale = typographyScale[variant];
  
  const getColorClass = () => {
    switch (color) {
      case 'primary':
        return 'text-label-primary-light dark:text-label-primary-dark';
      case 'secondary':
        return 'text-label-secondary-light/68 dark:text-label-secondary-dark/68';
      case 'tertiary':
        return 'text-label-tertiary-light/48 dark:text-label-tertiary-dark/48';
      case 'accent':
        return 'text-primary';
      case 'success':
        return 'text-system-green';
      case 'warning':
        return 'text-system-orange';
      case 'error':
        return 'text-system-red';
      default:
        return 'text-label-primary-light dark:text-label-primary-dark';
    }
  };

  const getWeightClass = () => {
    switch (weight) {
      case 'medium':
        return 'font-medium';
      case 'semibold':
        return 'font-semibold';
      case 'bold':
        return 'font-bold';
      default:
        return 'font-normal';
    }
  };

  return (
    <Text
      className={cn(
        getColorClass(),
        getWeightClass(),
        className
      )}
      style={[
        {
          fontSize: scale.fontSize,
          lineHeight: scale.lineHeight,
          fontWeight: weight === 'regular' ? scale.fontWeight : undefined,
        },
        style,
      ]}
      allowFontScaling={true}
      maxFontSizeMultiplier={1.5} // Limit scaling for layout stability
      {...props}
    >
      {children}
    </Text>
  );
}

// Specialized components for common use cases
export function Heading1({ children, className, ...props }: Omit<TypographyProps, 'variant'>) {
  return (
    <Typography 
      variant="largeTitle" 
      weight="bold" 
      className={cn("mb-2", className)} 
      {...props}
    >
      {children}
    </Typography>
  );
}

export function Heading2({ children, className, ...props }: Omit<TypographyProps, 'variant'>) {
  return (
    <Typography 
      variant="title1" 
      weight="bold" 
      className={cn("mb-2", className)} 
      {...props}
    >
      {children}
    </Typography>
  );
}

export function Heading3({ children, className, ...props }: Omit<TypographyProps, 'variant'>) {
  return (
    <Typography 
      variant="title2" 
      weight="semibold" 
      className={cn("mb-1", className)} 
      {...props}
    >
      {children}
    </Typography>
  );
}

export function BodyText({ children, className, ...props }: Omit<TypographyProps, 'variant'>) {
  return (
    <Typography 
      variant="body" 
      className={className} 
      {...props}
    >
      {children}
    </Typography>
  );
}

export function Caption({ children, className, ...props }: Omit<TypographyProps, 'variant'>) {
  return (
    <Typography 
      variant="caption1" 
      color="secondary" 
      className={className} 
      {...props}
    >
      {children}
    </Typography>
  );
}

export function Label({ children, className, ...props }: Omit<TypographyProps, 'variant'>) {
  return (
    <Typography 
      variant="subheadline" 
      weight="medium" 
      color="secondary" 
      className={className} 
      {...props}
    >
      {children}
    </Typography>
  );
}
