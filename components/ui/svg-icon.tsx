import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface SvgIconProps {
  svgPath: string;
  width?: number;
  height?: number;
  color?: string;
}

export function SvgIcon({ svgPath, width = 22, height = 22, color = 'currentColor' }: SvgIconProps) {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
      <Path d={svgPath} />
    </Svg>
  );
}