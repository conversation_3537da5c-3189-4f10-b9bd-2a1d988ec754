import React from 'react';
import { View, ViewStyle } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface TabScreenWrapperProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function TabScreenWrapper({ children, style }: TabScreenWrapperProps) {
  const insets = useSafeAreaInsets();
  
  // Calculate the total bottom padding needed:
  // - Safe area bottom inset
  // - 16px gap from safe area to floating navbar
  // - 64px height of floating navbar
  // - 16px additional padding for content breathing room
  const bottomPadding = insets.bottom + 16 + 64 + 16;

  return (
    <View
      style={[
        {
          flex: 1,
          paddingBottom: bottomPadding,
        },
        style,
      ]}
    >
      {children}
    </View>
  );
}
