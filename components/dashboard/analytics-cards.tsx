import { Bar<PERSON><PERSON> } from '@/components/ui/bar-chart';
import { DashboardCard } from '@/components/ui/dashboard-card';
import React from 'react';
import { Text, View } from 'react-native';

const focusTimeData = [
  { height: 60 }, { height: 15 }, { height: 25 }, { height: 65 },
  { height: 20 }, { height: 70 }, { height: 85, isActive: true }, { height: 45 },
  { height: 30 }, { height: 80 }, { height: 95 }, { height: 10 }
];

const tasksData = [
  { height: 40 }, { height: 60 }, { height: 30 }, { height: 80, isActive: true },
  { height: 40 }, { height: 30 }, { height: 10 }
];

interface AnalyticsCardProps {
  title: string;
  value: string;
  period: string;
  change: string;
  data: Array<{ height: number; isActive?: boolean }>;
}

function AnalyticsCard({ title, value, period, change, data }: AnalyticsCardProps) {
  return (
    <DashboardCard>
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-sm font-medium text-label-secondary-light/68 dark:text-label-secondary-dark/68">
          {title}
        </Text>
        <View className="flex-row items-center gap-1 text-sm">
          <Text className="font-medium text-system-green">{change}</Text>
        </View>
      </View>

      <Text className="text-3xl font-bold text-label-primary-light dark:text-label-primary-dark mb-2">
        {value}
      </Text>
      <Text className="text-label-secondary-light/68 dark:text-label-secondary-dark/68 text-sm mb-4">
        {period}
      </Text>

      <View className="h-32 bg-surface-light/80 dark:bg-surface-dark/80 rounded-xl p-3">
        <BarChart data={data} className="h-full" />
      </View>
    </DashboardCard>
  );
}

export function AnalyticsCards() {
  return (
    <View className="space-y-4 md:space-y-0 md:flex-row md:gap-6">
      <View className="w-full md:flex-1">
        <AnalyticsCard
          title="Focus Time"
          value="12h 30m"
          period="This Week"
          change="+15%"
          data={focusTimeData}
        />
      </View>

      <View className="w-full md:flex-1">
        <AnalyticsCard
          title="Tasks Completed"
          value="25"
          period="This Week"
          change="+20%"
          data={tasksData}
        />
      </View>
    </View>
  );
}
