import { But<PERSON> } from '@/components/ui/button';
import { DashboardCard } from '@/components/ui/dashboard-card';
import { StatCard } from '@/components/ui/stat-card';
import { Heading3 } from '@/components/ui/typography';
import React from 'react';
import { View } from 'react-native';

export function QuickStart() {
  return (
    <DashboardCard>
      <View className="flex-row items-center justify-between mb-6">
        <Heading3 className="flex-1">
          Quick Start
        </Heading3>
        <Button
          variant="primary"
          size="md"
          style={{
            shadowColor: '#F4C753',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 4,
            elevation: 3,
          }}
        >
          Focus
        </Button>
      </View>
      
      <View className="space-y-4 sm:space-y-0 sm:flex-row sm:gap-6">
        <View className="w-full sm:flex-1">
          <StatCard
            value="2h 15m"
            label="Today's Focus"
          />
        </View>
        <View className="w-full sm:flex-1">
          <StatCard
            value="🔥 12"
            label="Current Streak"
          />
        </View>
      </View>
    </DashboardCard>
  );
}
