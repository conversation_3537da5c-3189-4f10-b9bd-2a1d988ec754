import { Bar<PERSON><PERSON> } from '@/components/ui/bar-chart';
import { DashboardCard } from '@/components/ui/dashboard-card';
import { StatCard } from '@/components/ui/stat-card';
import { Heading3 } from '@/components/ui/typography';
import React, { useState } from 'react';
import { Pressable, Text, View } from 'react-native';

const weeklyData = [
  { height: 60, label: 'Mon' },
  { height: 80, label: 'Tue' },
  { height: 95, label: 'Wed', isActive: true },
  { height: 50, label: 'Thu' },
  { height: 70, label: 'Fri' },
  { height: 40, label: 'Sat' },
  { height: 20, label: 'Sun' },
];

export function ProgressSection() {
  const [activeTab, setActiveTab] = useState<'weekly' | 'monthly'>('weekly');

  return (
    <DashboardCard>
      <View className="flex-row items-center justify-between mb-6">
        <Heading3>
          My Progress
        </Heading3>

        <View className="flex-row items-center space-x-2 bg-surface-light/88 dark:bg-surface-dark/88 p-1 rounded-full text-sm font-medium">
          <Pressable
            onPress={() => setActiveTab('weekly')}
            className={`px-4 py-2 rounded-full ${
              activeTab === 'weekly'
                ? 'bg-elevated-light dark:bg-elevated-dark shadow-sm'
                : ''
            }`}
            style={activeTab === 'weekly' ? {
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.1,
              shadowRadius: 2,
            } : {}}
          >
            <Text className={`font-medium ${
              activeTab === 'weekly'
                ? 'text-label-primary-light dark:text-label-primary-dark'
                : 'text-label-secondary-light/68 dark:text-label-secondary-dark/68'
            }`}>Weekly</Text>
          </Pressable>

          <Pressable
            onPress={() => setActiveTab('monthly')}
            className={`px-4 py-2 rounded-full ${
              activeTab === 'monthly'
                ? 'bg-elevated-light dark:bg-elevated-dark shadow-sm'
                : ''
            }`}
            style={activeTab === 'monthly' ? {
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.1,
              shadowRadius: 2,
            } : {}}
          >
            <Text className={`font-medium ${
              activeTab === 'monthly'
                ? 'text-label-primary-light dark:text-label-primary-dark'
                : 'text-label-secondary-light/68 dark:text-label-secondary-dark/68'
            }`}>Monthly</Text>
          </Pressable>
        </View>
      </View>

      <View className="space-y-3 sm:space-y-0 sm:flex-row sm:gap-4 mb-8">
        <View className="w-full sm:flex-1">
          <StatCard value="15.5h" label="Weekly Study" />
        </View>
        <View className="w-full sm:flex-1">
          <StatCard value="28 days" label="Longest Streak" />
        </View>
        <View className="w-full sm:flex-1">
          <StatCard value="85%" label="Goal Comp." />
        </View>
      </View>

      <View className="w-full bg-surface-light/80 dark:bg-surface-dark/80 rounded-xl p-4">
        <View className="h-40 mb-4">
          <BarChart
            data={weeklyData}
            className="h-full"
          />
        </View>

        <View className="flex-row justify-between px-1">
          {weeklyData.map((day, index) => (
            <View key={index} className="flex-1 items-center">
              <Text
                className={`text-xs font-medium ${
                  day.isActive
                    ? 'text-primary font-bold'
                    : 'text-label-secondary-light/68 dark:text-label-secondary-dark/68'
                }`}
              >
                {day.label}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </DashboardCard>
  );
}
