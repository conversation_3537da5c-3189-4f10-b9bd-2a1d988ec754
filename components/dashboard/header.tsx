import { useTheme } from '@/context/theme-context';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export function DashboardHeader() {
  const insets = useSafeAreaInsets();
  const { isDark, toggleTheme } = useTheme();

  return (
    <View
      className="bg-background-light/96 dark:bg-background-dark/96 border-b border-separator-light/24 dark:border-separator-dark/24"
      style={{
        paddingTop: insets.top,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: isDark ? 0.3 : 0.1,
        shadowRadius: 4,
        elevation: 2,
      }}
    >
      <View className="flex-row items-center justify-between px-6 py-4">
        <View className="w-10" />

        <View className="flex-col items-center">
          <Text className="text-xl font-bold text-label-primary-light dark:text-label-primary-dark">
            IsotopeAI
          </Text>
          <Text className="text-xs text-label-secondary-light/68 dark:text-label-secondary-dark/68 font-medium">
            Dashboard
          </Text>
        </View>

        <Pressable
          onPress={toggleTheme}
          className="flex h-10 w-10 items-center justify-center rounded-xl bg-surface-light/88 dark:bg-surface-dark/88"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
          }}
        >
          <Ionicons
            name={isDark ? "sunny-outline" : "moon-outline"}
            size={20}
            color={isDark ? "#FFFFFF" : "#000000"}
          />
        </Pressable>
      </View>
    </View>
  );
}
