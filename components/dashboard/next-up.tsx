import { DashboardCard } from '@/components/ui/dashboard-card';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Pressable, Text, View } from 'react-native';

export function NextUp() {
  return (
    <DashboardCard>
      <Text className="text-xl font-bold mb-6 text-label-primary-light dark:text-label-primary-dark">
        Next Up
      </Text>

      <View className="flex-row items-center space-x-4 p-4 bg-surface-light/88 dark:bg-surface-dark/88 rounded-xl">
        <View
          className="p-3 rounded-xl"
          style={{
            backgroundColor: '#007AFF20',
          }}
        >
          <Ionicons
            name="flask-outline"
            size={24}
            color="#007AFF"
          />
        </View>

        <View className="flex-1">
          <Text className="font-semibold text-label-primary-light dark:text-label-primary-dark">
            Chemistry Lab Report
          </Text>
          <Text className="text-sm text-label-secondary-light/68 dark:text-label-secondary-dark/68">
            Due in 2 days
          </Text>
        </View>

        <Pressable
          className="p-3 rounded-full"
          style={{
            backgroundColor: '#F4C75320',
          }}
        >
          <Ionicons
            name="arrow-forward"
            size={24}
            color="#F4C753"
          />
        </Pressable>
      </View>
    </DashboardCard>
  );
}
