import { DashboardCard } from '@/components/ui/dashboard-card';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Pressable, Text, TextInput, View } from 'react-native';

interface Task {
  id: string;
  title: string;
  completed: boolean;
  urgent?: boolean;
}

const initialTasks: Task[] = [
  {
    id: '1',
    title: 'Review Quantum Mechanics notes',
    completed: false,
    urgent: true
  },
  {
    id: '2',
    title: 'Practice 10 integrals',
    completed: false
  },
  {
    id: '3',
    title: 'Daily meditation',
    completed: true
  }
];

export function TasksHabits() {
  const [tasks, setTasks] = useState<Task[]>(initialTasks);
  const [newTask, setNewTask] = useState('');

  const toggleTask = (id: string) => {
    setTasks(tasks.map(task => 
      task.id === id ? { ...task, completed: !task.completed } : task
    ));
  };

  const addTask = () => {
    if (newTask.trim()) {
      const task: Task = {
        id: Date.now().toString(),
        title: newTask.trim(),
        completed: false
      };
      setTasks([...tasks, task]);
      setNewTask('');
    }
  };

  return (
    <DashboardCard>
      <Text className="text-xl font-bold mb-6 text-label-primary-light dark:text-label-primary-dark">
        Today's Tasks & Habits
      </Text>

      <View className="space-y-4 mb-6">
        {tasks.map((task) => (
          <View
            key={task.id}
            className={`flex-row items-center space-x-4 p-3 bg-surface-light/88 dark:bg-surface-dark/88 rounded-xl ${
              task.completed ? 'opacity-75' : ''
            }`}
          >
            <Pressable
              onPress={() => toggleTask(task.id)}
              className={`w-6 h-6 border-2 rounded-full flex items-center justify-center ${
                task.completed
                  ? 'border-primary bg-primary/16'
                  : task.urgent
                  ? 'border-system-red'
                  : 'border-separator-light dark:border-separator-dark'
              }`}
            >
              {task.completed && (
                <Ionicons name="checkmark" size={16} color="#F4C753" />
              )}
            </Pressable>

            <Text
              className={`font-medium flex-1 ${
                task.completed
                  ? 'line-through text-label-secondary-light/68 dark:text-label-secondary-dark/68'
                  : 'text-label-primary-light dark:text-label-primary-dark'
              }`}
            >
              {task.title}
            </Text>

            {task.urgent && !task.completed && (
              <View
                className="px-3 py-1 rounded-full"
                style={{ backgroundColor: '#FF3B3020' }}
              >
                <Text className="text-xs font-medium text-system-red">Urgent</Text>
              </View>
            )}
          </View>
        ))}
      </View>
      
      <View className="relative">
        <TextInput
          value={newTask}
          onChangeText={setNewTask}
          placeholder="Add a quick task..."
          placeholderTextColor="#9CA3AF"
          className="w-full bg-surface-light/88 dark:bg-surface-dark/88 rounded-xl py-4 pl-4 pr-14 text-label-primary-light dark:text-label-primary-dark"
          onSubmitEditing={addTask}
        />
        <Pressable
          onPress={addTask}
          className="absolute right-2 top-1/2 p-2 rounded-full bg-primary -translate-y-1/2"
          style={{
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.2,
            shadowRadius: 4,
          }}
        >
          <Ionicons name="add" size={24} color="#000000" />
        </Pressable>
      </View>
    </DashboardCard>
  );
}
