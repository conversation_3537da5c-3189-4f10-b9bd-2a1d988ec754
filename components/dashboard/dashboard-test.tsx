import React from 'react';
import { View, ScrollView, Dimensions } from 'react-native';
import { DashboardCard } from '@/components/ui/dashboard-card';
import { StatCard } from '@/components/ui/stat-card';
import { Heading1, Heading2, Heading3, BodyText, Caption } from '@/components/ui/typography';
import { useResponsiveDimensions } from '@/hooks/use-responsive-dimensions';

const { width: screenWidth } = Dimensions.get('window');

export function DashboardTest() {
  const { isMobile, isTablet, isDesktop, columns, cardWidth, spacing } = useResponsiveDimensions();

  return (
    <ScrollView 
      className="flex-1 bg-background-light dark:bg-background-dark"
      contentContainerStyle={{ padding: 16 }}
    >
      {/* Screen Size Information */}
      <DashboardCard className="mb-6" accessibilityLabel="Screen size information">
        <Heading3 className="mb-4">Screen Size Test</Heading3>
        <View className="space-y-2">
          <BodyText>Screen Width: {screenWidth}px</BodyText>
          <BodyText>Device Type: {isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'}</BodyText>
          <BodyText>Columns: {columns}</BodyText>
          <BodyText>Card Width: {typeof cardWidth === 'number' ? `${cardWidth}px` : cardWidth}</BodyText>
          <BodyText>Spacing: {spacing}px</BodyText>
        </View>
      </DashboardCard>

      {/* Typography Test */}
      <DashboardCard className="mb-6" accessibilityLabel="Typography showcase">
        <Heading3 className="mb-4">Typography Test</Heading3>
        <View className="space-y-3">
          <Heading1>Large Title (34px)</Heading1>
          <Heading2>Title 1 (28px)</Heading2>
          <Heading3>Title 2 (22px)</Heading3>
          <BodyText>Body text with proper line height and spacing</BodyText>
          <Caption>Caption text for secondary information</Caption>
        </View>
      </DashboardCard>

      {/* Card Variants Test */}
      <View className="space-y-4 mb-6">
        <Heading3>Card Variants Test</Heading3>
        
        <DashboardCard 
          variant="default" 
          accessibilityLabel="Default card variant"
          className="mb-4"
        >
          <BodyText weight="semibold">Default Card</BodyText>
          <Caption>Standard padding and styling</Caption>
        </DashboardCard>

        <DashboardCard 
          variant="compact" 
          accessibilityLabel="Compact card variant"
          className="mb-4"
        >
          <BodyText weight="semibold">Compact Card</BodyText>
          <Caption>Reduced padding for dense layouts</Caption>
        </DashboardCard>

        <DashboardCard 
          variant="featured" 
          accessibilityLabel="Featured card variant"
          className="mb-4"
        >
          <BodyText weight="semibold">Featured Card</BodyText>
          <Caption>Enhanced shadow and larger padding</Caption>
        </DashboardCard>
      </View>

      {/* StatCard Responsive Test */}
      <DashboardCard className="mb-6" accessibilityLabel="Statistics cards test">
        <Heading3 className="mb-4">StatCard Responsive Test</Heading3>
        <View className="space-y-4 sm:space-y-0 sm:flex-row sm:gap-4">
          <View className="w-full sm:flex-1">
            <StatCard 
              value="2.5h" 
              label="Focus Time" 
              accessibilityLabel="Focus time: 2.5 hours"
            />
          </View>
          <View className="w-full sm:flex-1">
            <StatCard 
              value="85%" 
              label="Completion Rate" 
              accessibilityLabel="Completion rate: 85 percent"
            />
          </View>
          <View className="w-full sm:flex-1">
            <StatCard 
              value="12" 
              label="Streak Days" 
              accessibilityLabel="Streak: 12 days"
            />
          </View>
        </View>
      </DashboardCard>

      {/* Accessibility Test */}
      <DashboardCard className="mb-6" accessibilityLabel="Accessibility features test">
        <Heading3 className="mb-4">Accessibility Test</Heading3>
        <View className="space-y-3">
          <BodyText>✅ Semantic roles and labels</BodyText>
          <BodyText>✅ Dynamic Type support</BodyText>
          <BodyText>✅ High contrast colors</BodyText>
          <BodyText>✅ Touch target sizes (44px minimum)</BodyText>
          <BodyText>✅ Screen reader compatibility</BodyText>
        </View>
      </DashboardCard>

      {/* Performance Test */}
      <DashboardCard className="mb-6" accessibilityLabel="Performance features test">
        <Heading3 className="mb-4">Performance Features</Heading3>
        <View className="space-y-3">
          <BodyText>✅ Memoized components</BodyText>
          <BodyText>✅ Optimized animations</BodyText>
          <BodyText>✅ Responsive calculations cached</BodyText>
          <BodyText>✅ Efficient re-renders</BodyText>
          <BodyText>✅ Spring physics animations</BodyText>
        </View>
      </DashboardCard>

      {/* Interactive Test */}
      <View className="space-y-4 mb-6">
        <Heading3>Interactive Test</Heading3>
        <BodyText color="secondary">Tap the cards below to test interactions:</BodyText>
        
        <View className="space-y-4 sm:space-y-0 sm:flex-row sm:gap-4">
          <DashboardCard 
            className="w-full sm:flex-1" 
            interactive={true}
            accessibilityLabel="Interactive card 1"
            accessibilityHint="Tap to test interaction"
          >
            <BodyText weight="semibold" className="text-center">Tap Me!</BodyText>
            <Caption className="text-center">Interactive enabled</Caption>
          </DashboardCard>
          
          <DashboardCard 
            className="w-full sm:flex-1" 
            interactive={false}
            accessibilityLabel="Non-interactive card"
          >
            <BodyText weight="semibold" className="text-center">Static</BodyText>
            <Caption className="text-center">Interactive disabled</Caption>
          </DashboardCard>
        </View>
      </View>

      {/* Bottom spacing for navigation */}
      <View className="h-20" />
    </ScrollView>
  );
}
