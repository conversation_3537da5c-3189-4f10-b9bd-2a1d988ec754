@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
:root {
  --background: 248 247 246;
  --foreground: 17 24 28;
  --card: 255 255 255;
  --card-foreground: 17 24 28;
  --popover: 255 255 255;
  --popover-foreground: 17 24 28;
  --primary: 244 199 83;
  --primary-foreground: 34 29 16;
  --secondary: 243 244 246;
  --secondary-foreground: 75 85 99;
  --muted: 243 244 246;
  --muted-foreground: 107 114 128;
  --accent: 244 199 83;
  --accent-foreground: 34 29 16;
  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;
  --border: 229 231 235;
  --input: 243 244 246;
  --ring: 244 199 83;

  --android-background: 254 255 252;
  --android-foreground: 1 1 1;
  --android-card: 255 255 255;
  --android-card-foreground: 1 1 0;
  --android-popover: 249 253 244;
  --android-popover-foreground: 1 1 0;
  --android-primary: 0 30 127;
  --android-primary-foreground: 240 243 255;
  --android-secondary: 192 214 255;
  --android-secondary-foreground: 0 36 102;
  --android-muted: 0 0 0;
  --android-muted-foreground: 179 179 179;
  --android-accent: 92 43 109;
  --android-accent-foreground: 215 182 226;
  --android-destructive: 186 26 26;
  --android-destructive-foreground: 255 255 255;
  --android-border: 186 189 182;
  --android-input: 202 206 196;
  --android-ring: 186 189 182;

  --web-background: 251 254 246;
  --web-foreground: 31 33 28;
  --web-card: 255 255 255;
  --web-card-foreground: 24 28 35;
  --web-popover: 215 217 228;
  --web-popover-foreground: 0 0 0;
  --web-primary: 15 25 0;
  --web-primary-foreground: 246 255 232;
  --web-secondary: 176 201 255;
  --web-secondary-foreground: 28 60 114;
  --web-muted: 240 240 240;
  --web-muted-foreground: 122 122 122;
  --web-accent: 169 73 204;
  --web-accent-foreground: 255 255 255;
  --web-destructive: 186 26 26;
  --web-destructive-foreground: 255 255 255;
  --web-border: 235 237 233;
  --web-input: 243 244 241;
  --web-ring: 226 229 220;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: 34 29 16;
    --foreground: 229 231 235;
    --card: 28 28 30;
    --card-foreground: 229 231 235;
    --popover: 28 28 30;
    --popover-foreground: 229 231 235;
    --primary: 244 199 83;
    --primary-foreground: 34 29 16;
    --secondary: 55 65 81;
    --secondary-foreground: 209 213 219;
    --muted: 55 65 81;
    --muted-foreground: 156 163 175;
    --accent: 244 199 83;
    --accent-foreground: 34 29 16;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 55 65 81;
    --input: 55 65 81;
    --ring: 244 199 83;

    --android-background: 23 26 19;
    --android-foreground: 233 237 227;
    --android-card: 74 77 70;
    --android-card-foreground: 202 205 197;
    --android-popover: 74 77 70;
    --android-popover-foreground: 202 205 197;
    --android-primary: 150 255 3;
    --android-primary-foreground: 0 0 0;
    --android-secondary: 192 214 255;
    --android-secondary-foreground: 0 36 102;
    --android-muted: 0 0 0;
    --android-muted-foreground: 179 179 179;
    --android-accent: 92 43 109;
    --android-accent-foreground: 215 182 226;
    --android-destructive: 147 0 10;
    --android-destructive-foreground: 255 255 255;
    --android-border: 149 153 142;
    --android-input: 74 78 69;
    --android-ring: 149 153 142;

    --web-background: 21 25 17;
    --web-foreground: 230 236 223;
    --web-card: 70 74 78;
    --web-card-foreground: 197 201 206;
    --web-popover: 70 74 78;
    --web-popover-foreground: 197 201 206;
    --web-primary: 150 255 3;
    --web-primary-foreground: 13 23 0;
    --web-secondary: 28 60 114;
    --web-secondary-foreground: 255 255 255;
    --web-muted: 38 38 38;
    --web-muted-foreground: 179 179 179;
    --web-accent: 83 0 111;
    --web-accent-foreground: 255 255 255;
    --web-destructive: 147 0 10;
    --web-destructive-foreground: 255 255 255;
    --web-border: 62 67 55;
    --web-input: 52 58 43;
    --web-ring: 78 87 65;
  }
}
}