import { useEffect } from 'react';
import { useTheme } from '@/context/theme-context';

export function useDarkMode() {
  const { isDark } = useTheme();

  useEffect(() => {
    // For web compatibility, add/remove dark class to document
    if (typeof document !== 'undefined') {
      if (isDark) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  }, [isDark]);

  return { isDark };
}
