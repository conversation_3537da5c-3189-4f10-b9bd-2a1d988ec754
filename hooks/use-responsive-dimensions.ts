import { useState, useEffect, useCallback } from 'react';
import { Dimensions, ScaledSize } from 'react-native';

interface ResponsiveDimensions {
  width: number;
  height: number;
  isTablet: boolean;
  isDesktop: boolean;
  isMobile: boolean;
  columns: number;
  cardWidth: number;
  spacing: number;
}

const BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
} as const;

const SPACING = {
  mobile: 16,
  tablet: 20,
  desktop: 24,
} as const;

let cachedDimensions: ResponsiveDimensions | null = null;
let lastWidth = 0;

export function useResponsiveDimensions(): ResponsiveDimensions {
  const [dimensions, setDimensions] = useState<ResponsiveDimensions>(() => {
    const { width, height } = Dimensions.get('window');
    return calculateDimensions(width, height);
  });

  const handleDimensionChange = useCallback(({ window }: { window: ScaledSize }) => {
    const { width, height } = window;
    
    // Avoid unnecessary recalculations
    if (Math.abs(width - lastWidth) < 10) return;
    
    lastWidth = width;
    const newDimensions = calculateDimensions(width, height);
    
    // Only update if dimensions actually changed
    if (!cachedDimensions || 
        cachedDimensions.width !== newDimensions.width ||
        cachedDimensions.columns !== newDimensions.columns) {
      cachedDimensions = newDimensions;
      setDimensions(newDimensions);
    }
  }, []);

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', handleDimensionChange);
    return () => subscription?.remove();
  }, [handleDimensionChange]);

  return dimensions;
}

function calculateDimensions(width: number, height: number): ResponsiveDimensions {
  const isMobile = width < BREAKPOINTS.mobile;
  const isTablet = width >= BREAKPOINTS.mobile && width < BREAKPOINTS.tablet;
  const isDesktop = width >= BREAKPOINTS.tablet;

  // Calculate optimal number of columns
  let columns = 1;
  let spacing = SPACING.mobile;
  
  if (isMobile) {
    columns = 1;
    spacing = SPACING.mobile;
  } else if (isTablet) {
    columns = 2;
    spacing = SPACING.tablet;
  } else {
    columns = 3;
    spacing = SPACING.desktop;
  }

  // Calculate card width based on columns and spacing
  const totalSpacing = spacing * 2; // Container padding
  const gapSpacing = spacing * (columns - 1); // Gaps between cards
  const availableWidth = width - totalSpacing - gapSpacing;
  const cardWidth = availableWidth / columns;

  return {
    width,
    height,
    isMobile,
    isTablet,
    isDesktop,
    columns,
    cardWidth: Math.max(cardWidth, 280), // Minimum card width
    spacing,
  };
}

// Hook for optimized card dimensions
export function useCardDimensions() {
  const { cardWidth, spacing, isMobile } = useResponsiveDimensions();
  
  return {
    width: isMobile ? '100%' : cardWidth,
    marginBottom: spacing,
    minHeight: isMobile ? 120 : 140,
  };
}

// Hook for grid layout calculations
export function useGridLayout() {
  const { columns, spacing, isMobile } = useResponsiveDimensions();
  
  return {
    numColumns: columns,
    spacing,
    contentContainerStyle: {
      padding: spacing,
      paddingBottom: 100, // Extra space for bottom navigation
    },
    columnWrapperStyle: isMobile ? undefined : {
      justifyContent: 'space-between',
      marginBottom: spacing,
    },
  };
}
